import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';

const {width, height} = Dimensions.get('window');

interface ModelDoneLevelProps {
  visible: boolean;
  currentGem: number;
  currentCup: number;
  gemAdd: number;
  cupAdd: number;
  onNextLevel: () => void;
  message?: string;
}

const ModelDoneLevel = ({
  visible,
  onNextLevel,
  currentGem,
  currentCup,
  gemAdd,
  cupAdd,
  message = 'Winner',
}: ModelDoneLevelProps) => {
  return (
    <Modal visible={visible} transparent={true} animationType="fade">
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.iconContainer}>
            <Text style={styles.messageText}>{message}</Text>
            <View style={styles.pointContainer}>
              <View style={[styles.icon, styles.gemIcon]}>
                <Image source={require('../assets/big_gem.png')} />
              </View>
              <Text style={styles.textIcon}>
                {currentGem}+{gemAdd}
              </Text>
            </View>
            <View style={[styles.pointContainer, styles.cupContainer]}>
              <View style={[styles.icon, styles.cupIcon]}>
                <Image source={require('../assets/big_cup.png')} />
              </View>
              <Text style={styles.textIcon}>
                {currentCup}+{cupAdd}
              </Text>
            </View>
          </View>

          <View style={styles.birdContainer}>
            <Image source={require('../assets/winner_bird.png')} />
          </View>

          <TouchableOpacity style={styles.restartButton} onPress={onNextLevel}>
            <Image
              source={require('../assets/next_button.png')}
              style={styles.restartButton}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.69)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContent: {
    width: width * 0.93,
    minHeight: height * 0.8,
    backgroundColor: 'rgba(112, 90, 64, 0.96)',
    padding: 20,
    marginBottom: 20,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  iconContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  messageText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FDE832',
  },
  birdContainer: {
    marginVertical: 30,
    position: 'relative',
  },
  restartButton: {
    width: 230,
    height: 90,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pointContainer: {
    position: 'relative',
    backgroundColor: 'white',
    width: 130,
    marginLeft: 24,
    marginTop: 40,
    height: 32,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cupContainer: {
    marginTop: 36,
  },
  icon: {
    position: 'absolute',
    zIndex: 1,
    left: 0,
    transform: [{translateX: -10}, {translateY: -3}],
    alignItems: 'center',
    justifyContent: 'center',
    width: 25,
    height: 25,
  },
  gemIcon: {
    top: 6,
  },
  cupIcon: {
    top: 0,
  },
  textIcon: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default ModelDoneLevel;
