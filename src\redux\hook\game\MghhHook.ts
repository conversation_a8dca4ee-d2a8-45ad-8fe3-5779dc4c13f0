import {useDispatch} from 'react-redux';
import {reset, startGame, setData, nextQuestion, nextLevel, restartLevel} from '../../reducers/game/MGHHReducer';

export const useMghhHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    nextQuestion: () => {
      dispatch(nextQuestion());
    },
    nextLevel: () => {
      dispatch(nextLevel());
    },
    restartLevel: () => {
      dispatch(restartLevel());
    },
    reset: () => {
      dispatch(reset());
    },
  };

  return action;
};
