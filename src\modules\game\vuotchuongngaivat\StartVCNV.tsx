import {
  Keyboard,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {MiniQuestion, Word} from './models/models';
import {PanGestureHandler} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import React, {useEffect, useRef, useState} from 'react';
import HeadGame from '../components/HeadGame';
import Lives from '../components/Lives';
import {CardTitleGame} from '../components/CardTitleGame';
import {BottomGame} from '../components/BottomGame';
import {useVcnvHook} from './redux/hooks/vcnvHook';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {checkPositionOrder, replaceObjectById} from '../utils/functions';
import {useGameHook} from '../../../redux/hook/gameHook';
import GameOverModal from '../components/GameOverModel';
import HintModel from '../components/HintModel';
import ModelConfirm from '../components/ModelConfirm';
import ModelPauseGame from '../components/ModelPauseGame';
import {useRoute} from '@react-navigation/native';
import ConfigAPI from '../../../Config/ConfigAPI';

interface DropZone {
  id: string;
  miniQuestionId: string;
  word?: Word | null;
  index: number;
}

interface StartVCNVProps {
  gameId: string;
  stage: number;
  competenceId: string;
}

// DraggableWord component moved outside to avoid recreation on every render
const DraggableWord = React.memo(({word, refDropZone, onDrop}: {
  word: Word;
  refDropZone: React.MutableRefObject<{[key: string]: View | null}>;
  onDrop: (word: Word, dropZoneId: string) => void;
}) => {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);
  const zIndex = useSharedValue(0);

  const checkAndHandleDrop = (
    eventAbsoluteX: number,
    eventAbsoluteY: number,
  ) => {
    const addSize = 10;
    Object.keys(refDropZone.current).forEach(id => {
      const refCurrent = refDropZone.current[id];
      if (refCurrent) {
        refCurrent.measureInWindow((x, y, width, height) => {
          const wordX = eventAbsoluteX;
          const wordY = eventAbsoluteY;
          const dropZoneX = x;
          const dropZoneY = y;
          const dropZoneRight = x + width + addSize;
          const dropZoneBottom = y + height + addSize;
          if (
            wordX >= dropZoneX &&
            wordX <= dropZoneRight &&
            wordY >= dropZoneY &&
            wordY <= dropZoneBottom
          ) {
            onDrop(word, id);
          }
        });
      }
    });
  };

  const gestureHandler = useAnimatedGestureHandler({
    onStart: _ => {
      'worklet';
      scale.value = withSpring(1.1);
      zIndex.value = 1000;
    },
    onActive: event => {
      'worklet';
      translateX.value = event.translationX;
      translateY.value = event.translationY;
    },
    onEnd: event => {
      'worklet';
      runOnJS(checkAndHandleDrop)(event.absoluteX, event.absoluteY);

      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
      scale.value = withSpring(1);
      zIndex.value = 0;
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    'worklet';
    return {
      transform: [
        {translateX: translateX.value},
        {translateY: translateY.value},
        {scale: scale.value},
      ],
      zIndex: zIndex.value,
    };
  });

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[styles.wordContainer, animatedStyle]}>
        <View
          style={{
            height: 36,
            width: 36,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text style={styles.wordText}>{word.text}</Text>
        </View>
      </Animated.View>
    </PanGestureHandler>
  );
});

const StartVCNV = () => {
  const vcnvHook = useVcnvHook();
  const gameHook = useGameHook();
  const {
    listMiniQuestion,
    currentQuestion,
  } = useSelector((state: RootState) => state.VTNVStore);
  const {
    currentLives,
    totalLives,
    gem,
    gemUse,
    gemAdd,
    isGameOver,
    messageGameOver,
  } = useSelector((state: RootState) => state.gameStore);
  const route = useRoute<any>();
  const gameId =  ConfigAPI.gameVTNV;
  const milestoneId = route.params?.milestoneId || 1;
  const competenceId = route.params?.competenceId || '';

  const [listWord, setListWord] = useState<Word[]>([]);
  const [listDropZone, setListDropZone] = useState<DropZone[]>([]);
  const [currentMiniQuestion, setCurrentMiniQuestion] =
    useState<MiniQuestion | null>(null);
  const [answer, setAnswer] = useState<string>('');
  const [isError, setIsError] = useState<boolean>(false);
  const [isCorrect, setIsCorrect] = useState<boolean>(false);
  const [statusMiniQuestion, setStatusMiniQuestion] = useState<
    'correct' | 'wrong' | null
  >(null);
  const [isShowModelConfirm, setIsShowModelConfirm] = useState<boolean>(false);
  const [isShowHintModel, setIsShowHintModel] = useState<boolean>(false);
  const [isPauseGame, setIsPauseGame] = useState<boolean>(false);
  const [isShowKeyboard, setIsShowKeyboard] = useState<boolean>(false);
  const [listMiniQuestionDone, setListMiniQuestionDone] = useState<
    MiniQuestion[]
  >([]);
  const [gemToAdd, setGemToAdd] = useState<number>(0);

  const refDropZone = useRef<{[key: string]: View | null}>({});
  const hiddenInputRef = useRef<TextInput | null>(null);

  useEffect(() => {
    initializeGame();
    Keyboard.addListener('keyboardDidShow', () => {
      setIsShowKeyboard(true);
    });
    Keyboard.addListener('keyboardDidHide', () => {
      setIsShowKeyboard(false);
      hiddenInputRef.current?.blur();
    });
    return () => {
      refDropZone.current = {};
      Keyboard.removeAllListeners('keyboardDidShow');
      Keyboard.removeAllListeners('keyboardDidHide');
    };
  }, [gameId, milestoneId, competenceId]);

  useEffect(() => {
    if (currentLives < 1) gameOver('Thất bại rồi, làm lại nào');
  }, [currentLives]);

  useEffect(() => {
    if (listMiniQuestionDone.length === listMiniQuestion?.length) {
      winnerGame();
    }
  }, [listMiniQuestionDone]);

  const resetState = () => {
    setListWord([]);
    setListDropZone([]);
    setCurrentMiniQuestion(null);
    setIsCorrect(false);
    setIsError(false);
    setStatusMiniQuestion(null);
    setIsShowKeyboard(false);
    refDropZone.current = {};
    hiddenInputRef.current?.blur();
  };

  const initializeGame = async () => {
    try {
      resetState();
      gameHook.restartGame();

      // Initialize game with API data
      const result = await vcnvHook.initializeGame({
        gameId,
        milestoneId,
        competenceId,
      });

      if (!result.success) {
        console.error('Failed to initialize game:', result.error);
        // Fallback to legacy data
        vcnvHook.startGame();
      }
    } catch (error) {
      console.error('Error initializing game:', error);
      // Fallback to legacy data
      vcnvHook.startGame();
    }
  };

  const startGame = () => {
    resetState();
    vcnvHook.startGame();
    gameHook.restartGame();
  };

  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  const winnerGame = () => {
    gameHook.gameOver('Chúc mừng bạn đã hoàn thành trò chơi');
    gameHook.setData({stateName: 'gem', value: gem + gemToAdd});
  };

  // Tạm dừng game
  const onPauseGame = () => {
    gameHook.pauseGame();
    setIsPauseGame(true);
  };

  // Tiếp tục game
  const onContinueGame = () => {
    gameHook.continueGame();
    setIsPauseGame(false);
  };

  // Hiển thị bàn phím
  const showKeyboard = () => {
    setIsError(false);
    setIsCorrect(false);
    setAnswer('');
    if (hiddenInputRef.current) {
      hiddenInputRef.current.focus();
      refDropZone.current = {};
      setCurrentMiniQuestion(null);
      setTimeout(() => {
        setListWord([]);
      }, 200);
    }
  };

  // Hiển thị modal gợi ý
  const onShowHintModel = () => {
    gameHook.setData({stateName: 'gem', value: gem - gemUse});
    setIsShowHintModel(true);
  };

  // Đáp án sai
  const onWrongAnswer = (type: 'normal' | 'key') => {
    if (type === 'normal') {
      resetQuestion();
      setStatusMiniQuestion('wrong');
      setTimeout(() => {
        setStatusMiniQuestion(null);
      }, 3000);
    } else {
      setIsError(true);
    }
    gameHook.setData({stateName: 'currentLives', value: currentLives - 1});
  };

  // Đáp án đúng
  const onCorrectAnswer = (type: 'normal' | 'key') => {
    if (type === 'normal') {
      setStatusMiniQuestion('correct');
      setGemToAdd(prev => prev + gemAdd);
      setListMiniQuestionDone(prev => [
        ...prev,
        currentMiniQuestion as MiniQuestion,
      ]);
      setTimeout(() => {
        setStatusMiniQuestion(null);
      }, 2000);
    } else {
      setIsCorrect(true);
      setGemToAdd(prev => prev + 20);
      setTimeout(() => {
        setIsCorrect(false);
        winnerGame();
      }, 2000);

    }
  };

  // Reset câu hỏi
  const resetQuestion = () => {
    if (!currentMiniQuestion?.id) return;

    const newListWord: Word[] = listDropZone
      .filter(
        zone => zone.miniQuestionId === currentMiniQuestion.id && zone.word,
      )
      .map(zone => zone.word as Word);

    setListWord(newListWord);

    const updatedListDropZone = listDropZone.map(zone =>
      zone.miniQuestionId === currentMiniQuestion.id && zone.word
        ? {...zone, word: null}
        : zone,
    );
    setListDropZone(updatedListDropZone);

    const updatedListMiniQuestion = replaceObjectById(
      {...currentMiniQuestion, listWord: newListWord},
      listMiniQuestion,
    );

    vcnvHook.setData({
      stateName: 'listMiniQuestion',
      value: updatedListMiniQuestion,
    });
  };

  // Khi chọn hàng ngang
  const chooseRow = (miniQuestion: MiniQuestion) => {
    refDropZone.current = {};
    setCurrentMiniQuestion(miniQuestion);
    setTimeout(() => {
      setListWord(miniQuestion.listWord);
    }, 200);
  };

  // Thả word vào dropzone
  const addWordToDropZone = React.useCallback((word: Word, dropZoneId: string) => {
    // Thêm word vào dropZone
    const dropZone = listDropZone.find(zone => zone.id === dropZoneId);
    if (dropZone) {
      if (dropZone.word) return;
      dropZone.word = word;
      const newListDropZone = replaceObjectById(dropZone, listDropZone);
      setListDropZone(newListDropZone);

      // Xoá word khỏi listWord
      const newListWord = listWord.filter(w => w.id !== word.id);
      setListWord(newListWord);

      // Sửa lại listMiniQuestion
      const cloneCurrentMiniQuestion = {...currentMiniQuestion};
      if (cloneCurrentMiniQuestion) {
        cloneCurrentMiniQuestion.listWord = [...newListWord];
        const newListMiniQuestion = replaceObjectById(
          cloneCurrentMiniQuestion,
          listMiniQuestion,
        );
        vcnvHook.setData({
          stateName: 'listMiniQuestion',
          value: newListMiniQuestion,
        });
      }

      // nếu listWord rỗng thì check answer
      if (newListWord.length === 0) {
        checkAnswerRow();
      }
    }
  }, [listDropZone, listWord, currentMiniQuestion, listMiniQuestion, vcnvHook, checkAnswerRow]);

  // CLick vào dropzone có chứa text
  const clickDropZoneWord = (dropZone: DropZone) => {
    const newListWord = [...listWord];
    if (newListWord) {
      newListWord.push(dropZone.word as Word);
    }
    // Thêm word vào listWord
    setListWord(newListWord);

    // // Sửa lại listMiniQuestion
    const cloneCurrentMiniQuestion = {...currentMiniQuestion};
    cloneCurrentMiniQuestion.listWord = newListWord;
    const newListMiniQuestion = replaceObjectById(
      cloneCurrentMiniQuestion,
      listMiniQuestion,
    );
    vcnvHook.setData({
      stateName: 'listMiniQuestion',
      value: newListMiniQuestion,
    });

    dropZone.word = null;
    const newListDropZone = replaceObjectById(dropZone, listDropZone);
    setListDropZone(newListDropZone);
  };

  // Kiểm tra kết quả của câu hàng ngang
  const checkAnswerRow = React.useCallback(() => {
    const zoneOfCurrentMiniQuestion = listDropZone.filter(
      zone => zone.miniQuestionId === currentMiniQuestion?.id && zone.word,
    );
    zoneOfCurrentMiniQuestion.sort((a, b) => a.index - b.index);

    const wordsInOrder = zoneOfCurrentMiniQuestion.map(zone => zone.word);

    const isAnswerCorrect = checkPositionOrder(wordsInOrder);
    if (isAnswerCorrect) {
      onCorrectAnswer('normal');
    } else {
      onWrongAnswer('normal');
    }
  }, [listDropZone, currentMiniQuestion, onCorrectAnswer, onWrongAnswer]);

  // Kiểm tra đáp án hàng dọc key
  const checkAnswer = () => {
    setIsError(false);
    setIsCorrect(false);
    if (answer.toLowerCase() === currentQuestion?.answer) {
      setIsCorrect(true);
      // setTimeout(() => {
      //   resetQuestion();
      //   vcnvHook.setData({stateName: 'questionDone', value: questionDone + 1});
      //   vcnvHook.nextQuestion();
      // }, 2000);
    } else {
      onWrongAnswer('key');
    }
  };



  // Render drop zone
  const renderDropZone = (index: number, miniQuestion: MiniQuestion) => {
    index = index + 1;

    // Tính toán để luôn có cột Key ở giữa (cột 5)
    const keyColumnIndex = 5; // Cột Key cố định ở giữa
    const totalWords = miniQuestion.length;
    const keyWordPosition = miniQuestion.keyIndex; // Vị trí của từ key trong câu (1-based)

    // Tính toán vị trí bắt đầu để cột Key luôn hiển thị
    // Đảm bảo từ key luôn nằm ở cột 5
    const startColumn = keyColumnIndex - keyWordPosition + 1;
    const endColumn = startColumn + totalWords - 1;

    // Kiểm tra ô này có được hiển thị không
    const isShow = index >= startColumn && index <= endColumn;

    // Kiểm tra ô này có phải là cột Key không
    const isKeyColumn = index === keyColumnIndex;

    const isChoose =
      currentMiniQuestion?.id === miniQuestion.id ||
      (isKeyColumn && isShowKeyboard);
    const dropZone = listDropZone.find(
      zone => zone.id === `${miniQuestion.id}${index}`,
    );
    if (!dropZone) {
      listDropZone.push({
        id: `${miniQuestion.id}${index}`,
        miniQuestionId: miniQuestion.id,
        word: null,
        index,
      });
    }
    return isShow ? (
      <TouchableOpacity
        onPress={() => {
          if (listMiniQuestionDone.find(i => i.id === miniQuestion.id)) return;
          if (isChoose && dropZone?.word) return clickDropZoneWord(dropZone);
          chooseRow(miniQuestion);
        }}
        ref={ref => {
          if (
            miniQuestion.id === currentMiniQuestion?.id &&
            refDropZone.current
          ) {
            refDropZone.current[`${miniQuestion.id}${index}`] = ref;
          }
        }}
        key={index}
        style={[
          {
            width: 36,
            height: 36,
            margin: 1,
            borderRadius: 4,
            backgroundColor: isKeyColumn ? '#1BDB55' : 'white',
          },

          isChoose
            ? {
                borderWidth: 1,
                borderColor: '#AE2B26',
              }
            : {
                backgroundColor: isKeyColumn ? '#1BDB55' : '#E8E8E8',
              },
        ]}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text style={styles.wordText}>{dropZone?.word?.text || ''}</Text>
        </View>
      </TouchableOpacity>
    ) : (
      <View key={index} style={{width: 36, height: 36, margin: 1}}></View>
    );
  };

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#FFC670'}}>
      <View style={{flex: 1}}>
        {/* Header */}
        <View style={{marginHorizontal: 16}}>
          <HeadGame
            timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
            isShowSuggest={true}
            onUseHint={() => setIsShowModelConfirm(true)} gameId={''}          />
          <View style={{marginTop: 8}}>
            <Lives totalLives={totalLives} currentLives={currentLives} />
          </View>
        </View>
 b
        {/* Body */}
        <View
          style={{
            flex: 1,
            marginHorizontal: 12,
            borderRadius: 16,
          }}>
          <View style={{marginVertical: 6}}>
            {statusMiniQuestion === 'correct' && (
              <Text style={styles.correctText}>Đáp án chính xác</Text>
            )}
            {statusMiniQuestion === 'wrong' && (
              <Text style={styles.errorText}>Sai rồi, hãy thử đáp án khác</Text>
            )}
          </View>
          {listMiniQuestion?.map(
            (miniQuestion: MiniQuestion, rowIndex: number) => {
              return (
                <View style={{flexDirection: 'row'}} key={rowIndex}>
                  {Array.from({length: 9}).map((_, colIndex) => {
                    return renderDropZone(colIndex, miniQuestion);
                  })}
                </View>
              );
            },
          )}
          <View style={{flex: 1, justifyContent: 'flex-end'}}>
            {!isShowKeyboard ? (
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  marginTop: 16,
                }}>
                <TouchableOpacity
                  style={[styles.checkButton, {backgroundColor: '#1BDB55'}]}
                  onPress={showKeyboard}>
                  <Text style={styles.checkButtonText}>Giải từ khoá</Text>
                </TouchableOpacity>
              </View>
            ) : null}
            <View>
              <CardTitleGame
                showIcon
                title={
                  !currentMiniQuestion
                    ? currentQuestion?.text || ''
                    : currentMiniQuestion?.text || ''
                }
              />
            </View>
            <View style={styles.wordsContainer}>
              {listWord.map((word: Word, index) => (
                <DraggableWord
                  key={index}
                  word={word}
                  refDropZone={refDropZone}
                  onDrop={addWordToDropZone}
                />
              ))}
            </View>
          </View>

          {isShowKeyboard ? (
            <View
              style={{
                width: '100%',
                height: 200,
                marginTop: 50,
                position: 'absolute',
              }}>
              <View
                style={[
                  styles.answerContainer,
                  isError ? styles.answerContainerError : null,
                  isCorrect ? styles.answerContainerCorrect : null,
                ]}>
                {answer.length < 1 ? (
                  <Text
                    style={{
                      color: '#999',
                      textAlign: 'center',
                      fontSize: 14,
                      fontStyle: 'italic',
                    }}>
                    Vui lòng nhập đáp án
                  </Text>
                ) : (
                  <Text style={styles.answerText}>{answer}</Text>
                )}
                {isCorrect && (
                  <Text style={styles.correctText}>Đáp án chính xác</Text>
                )}
                {isError && (
                  <Text style={styles.errorText}>
                    Sai rồi, hãy thử đáp án khác
                  </Text>
                )}
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  marginTop: 16,
                }}>
                <View style={{width: 16}}></View>
                <TouchableOpacity
                  style={[styles.checkButton, {backgroundColor: '#D32F2F'}]}
                  onPress={checkAnswer}>
                  <Text style={styles.checkButtonText}>Kiểm tra đáp án</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}
        </View>
        {/* Bottom */}
        <View style={styles.bottomContainer}>
          <BottomGame
            resetGame={startGame}
            backGame={() => {}}
            pauseGame={onPauseGame}
            volumeGame={() => {}}
          />
        </View>
        <TextInput
          ref={hiddenInputRef}
          style={styles.hiddenInput}
          value={answer}
          onChangeText={text => setAnswer(text)}
          autoCapitalize="none"
          autoCorrect={false}
          spellCheck={false}
          caretHidden={true}
        />
      </View>
      <View style={styles.modalContainer}>
        <ModelConfirm
          isShow={isShowModelConfirm}
          closeModal={() => setIsShowModelConfirm(false)}
          onConfirm={onShowHintModel}
        />
        <HintModel
          isShow={isShowHintModel}
          closeModal={() => setIsShowHintModel(false)}
          text={currentQuestion?.hint || ''}
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={startGame}
          message={messageGameOver}
          isTimeOut={false}
        />
        <ModelPauseGame
          visible={isPauseGame}
          message={'Bạn đang tạm dừng trò chơi'}
          onContinue={onContinueGame}
        />
      </View>
    </SafeAreaView>
  );
};
 export default StartVCNV;

const styles = StyleSheet.create({
  wordContainer: {
    margin: 6,
    backgroundColor: '#FCF8E8',
    borderRadius: 10,
    shadowColor: '#32325D',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wordText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
  },
  wordsContainer: {
    marginTop: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 70,
  },
  checkButton: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignSelf: 'center',
    marginBottom: 30,
  },
  checkButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    marginHorizontal: 16,
  },
  answerContainer: {
    position: 'relative',
    alignItems: 'flex-start',
    marginTop: 32,
    backgroundColor: '#FCF8E8',
    borderRadius: 15,
    paddingVertical: 32,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  answerContainerError: {
    borderColor: '#FF6B6B',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerContainerCorrect: {
    borderColor: '#2EB553',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',
    backgroundColor: '#FDE83280',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginTop: 5,
  },
  correctText: {
    fontSize: 12,
    textAlign: 'center',
    backgroundColor: '#FDE83280',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#2EB553',
    fontWeight: 'bold',
    marginTop: 5,
  },
  modalContainer: {
    zIndex: 1000,
  },
  hiddenInput: {
    width: 0, // Chiều rộng 0
    height: 0, // Chiều cao 0
    opacity: 0, // Hoàn toàn trong suốt
    position: 'absolute', // Không chiếm không gian trong layout
    // Để đảm bảo nó thực sự không nhìn thấy và không tương tác ngoài ý muốn:
    top: -10000, // Đẩy ra rất xa màn hình
    left: -10000,
  },
});

