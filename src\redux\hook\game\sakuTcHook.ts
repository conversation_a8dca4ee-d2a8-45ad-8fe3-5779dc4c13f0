import {useDispatch} from 'react-redux';
import {setData, startGame, nextQuestion, nextLevel, restartLevel} from '../../reducers/game/sakuTcReducer';

export const useSakuTcHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    nextQuestion: () => {
      dispatch(nextQuestion());
    },
    nextLevel: () => {
      dispatch(nextLevel());
    },
    restartLevel: () => {
      dispatch(restartLevel());
    },
  };

  return action;
};
