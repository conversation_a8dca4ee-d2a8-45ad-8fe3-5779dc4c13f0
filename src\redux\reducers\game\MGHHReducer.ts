import {createSlice} from '@reduxjs/toolkit';
import {
  DropZone,
  Question,
} from '../../../modules/game/manhghephoanhao/models/models';

interface State {
  dataListQuestion: Question[];
  listQuestions: Question[];
  currentQuestion: Question | null;
  currentLevel: number;
  totalQuestion: number;
  questionDone: number;
}

const data: Question[] = [
  {
    id: '1',
    level: 1,
    title: 'Tôi đã ăn sáng 1',
    description: 'Tôi đã ăn sáng 1',
    hint: 'Tôi đã ăn sáng 1',
    listWords: [
      {id: '1', position: 1, text: 'Tôi'},
      {id: '2', position: 2, text: 'đã'},
      {id: '3', position: 3, text: 'ăn'},
      {id: '4', position: 4, text: 'sáng'},
    ],
    lastQuestion: false,
  },
  {
    id: '2',
    level: 1,
    title: 'Tôi đã ăn sáng 2',
    description: 'Tôi đã ăn sáng 2',
    hint: 'Tôi đã ăn sáng 2',
    listWords: [
      {id: '1', position: 1, text: 'Tôi'},
      {id: '2', position: 2, text: 'đã'},
      {id: '3', position: 3, text: 'ăn'},
      {id: '4', position: 4, text: 'sáng'},
    ],
    lastQuestion: true,
  },
  {
    id: '3',
    level: 2,
    title: 'Tôi đã ăn trưa 1',
    description: 'Tôi đã ăn trưa 1',
    hint: 'Tôi đã ăn trưa 1',
    listWords: [
      {id: '1', position: 1, text: 'Tôi'},
      {id: '2', position: 2, text: 'đã'},
      {id: '3', position: 3, text: 'ăn'},
      {id: '4', position: 4, text: 'trưa'},
    ],
    lastQuestion: false,
  },
  {
    id: '4',
    level: 2,
    title: 'Tôi đã ăn trưa 2',
    description: 'Tôi đã ăn trưa 2',
    hint: 'Tôi đã ăn trưa 2',
    listWords: [
      {id: '1', position: 1, text: 'Tôi'},
      {id: '2', position: 2, text: 'đã'},
      {id: '3', position: 3, text: 'ăn'},
      {id: '4', position: 4, text: 'trưa'},
    ],
    lastQuestion: true,
  },
  {
    id: '5',
    level: 3,
    title: 'Tôi đã ăn tối 1',
    description: 'Tôi đã ăn tối 1',
    hint: 'Tôi đã ăn tối 1',
    listWords: [
      {id: '1', position: 1, text: 'Tôi'},
      {id: '2', position: 2, text: 'đã'},
      {id: '3', position: 3, text: 'ăn'},
      {id: '4', position: 4, text: 'tối'},
    ],
    lastQuestion: true,
  },
  {
    id: '6',
    level: 3,
    title: 'Tôi đã ăn tối 2',
    description: 'Tôi đã ăn tối 2',
    hint: 'Tôi đã ăn tối 2',
    listWords: [
      {id: '1', position: 1, text: 'Tôi'},
      {id: '2', position: 2, text: 'đã'},
      {id: '3', position: 3, text: 'ăn'},
      {id: '4', position: 4, text: 'tối'},
    ],
    lastQuestion: false,
  },
];

const initialState: State = {
  dataListQuestion: data,
  listQuestions: [],
  currentQuestion: null,
  currentLevel: 0,
  totalQuestion: 0,
  questionDone: 0,
};

export const MGHHReducer = createSlice({
  name: 'MGHHReducer',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    startGame: state => {
      state.currentLevel = 1;
      const questionLevel = state.dataListQuestion.filter(
        question => question.level === state.currentLevel,
      );
      state.listQuestions = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.totalQuestion = questionLevel.length;
      state.questionDone = 0;
    },
    nextQuestion: state => {
      state.currentQuestion = state.listQuestions[state.questionDone];
    },
    nextLevel: state => {
      state.currentLevel = state.currentLevel + 1;
      const questionLevel = state.dataListQuestion.filter(
        question => question.level === state.currentLevel,
      );
      state.listQuestions = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.questionDone = 0;
      state.totalQuestion = questionLevel.length;
    },
    restartLevel: state => {
      state.currentQuestion = state.listQuestions[0];
      state.questionDone = 0;
    },
    reset: state => {},
  },
});

export const {
  setData,
  reset,
  startGame,
  nextQuestion,
  nextLevel,
  restartLevel,
} = MGHHReducer.actions;

export default MGHHReducer.reducer;
