import React, {useState, useRef, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  Alert,
} from 'react-native';
import {PanGestureHandler} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import GameOverModal from '../components/GameOverModel';
import UseSupportModel from '../components/UseSupportModel';
import {SafeAreaView} from 'react-native-safe-area-context';
import {CardText} from '../components/CardText';
import {useSelector} from 'react-redux';
import {useMghhHook} from '../../../redux/hook/game/MghhHook';
import {useGameHook} from '../../../redux/hook/gameHook';
import {RootState} from '../../../redux/store/store';
import {BottomGame} from '../components/BottomGame';
import {CardTitleGame} from '../components/CardTitleGame';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import ConfigAPI from '../../../Config/ConfigAPI';
import {
  checkPositionOrder,
  removeObjectByType,
  replaceObjectById,
} from '../utils/functions';
import {DropZone, Word} from './models/models';
import CountBadge from '../components/CountQuestions';
import ModelDescriptionQuestion from '../components/ModelDescriptionQuestion';
import ModelDoneLevel from '../components/ModelDoneLevel';

const StartMGHH = () => {
  const {isGameOver, messageGameOver} = useSelector(
    (state: RootState) => state.gameStore,
  );
  const {currentQuestion, questionDone, totalQuestion, currentLevel} =
    useSelector((state: RootState) => state.MGHHStore);

  // const [showModelSupport, setShowModelSupport] = useState(false);
  const [answersState, setAnswersState] = useState<Word[]>([]);
  const [listDropZone, setListDropZone] = useState<DropZone[]>([]);
  const [textAnswer, setTextAnswer] = useState<string>('');
  const [isQuestionError, setIsQuestionError] = useState(false);
  const [isWinnerLevel, setIsWinnerLevel] = useState(false);
  const [isWinnerQuestion, setIsWinnerQuestion] = useState(false);

  const gameHook = useGameHook();
  const mghhHook = useMghhHook();
  const refDropZone = useRef<{[key: string]: View | null}>({});

  useEffect(() => {
    startGame();

    return () => {
      refDropZone.current = {};
    };
  }, []);

  useEffect(() => {
    if (currentQuestion) {
      setTextAnswer('');
      setAnswersState(currentQuestion.listWords);
      setListDropZone(
        currentQuestion.listWords.map(() => ({
          id: Math.random().toString(),
          word: null,
          x: 0,
          y: 0,
          width: 0,
          height: 0,
        })),
      );
    }
  }, [currentQuestion]);

  const startGame = () => {
    mghhHook.startGame();
    gameHook.restartGame();
  };

  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // sang câu hỏi tiếp theo
  const nextQuestion = () => {
    setIsWinnerQuestion(false);
    mghhHook.nextQuestion();
  };

  // thắng level
  const onWinnerLevel = () => {
    setIsWinnerLevel(true);
    // thêm gem ở đây
  };

  // sang level tiếp theo
  const onNextLevel = () => {
    setIsWinnerLevel(false);
    mghhHook.nextLevel();
  };

  const winnerGame = () => {};

  // bắt đầu lại level
  const restartLevel = () => {
    setIsQuestionError(false);
    mghhHook.restartLevel();
  };

  // Kiểm tra đáp án
  const checkAnswer = () => {
    const words: Word[] = [];
    listDropZone.forEach(zone => {
      if (zone.word) {
        words.push(zone.word);
      }
    });
    if (words.length !== currentQuestion?.listWords.length) return;
    const isCorrect = checkPositionOrder(words);
    if (isCorrect) {
      mghhHook.setData({
        stateName: 'questionDone',
        value: questionDone + 1,
      });
      if (currentQuestion?.lastQuestion) return onWinnerLevel();
      setIsWinnerQuestion(true);
    } else {
      const textAnswer = words.map(word => word.text).join(' ');
      setTextAnswer(textAnswer);
      setIsQuestionError(true);
      mghhHook.setData({stateName: 'currentQuestion', value: null});
    }
  };

  // Hàm xử lý khi thả từ vào drop zone
  const handleDropToZone = (dropZoneId: string, word: Word) => {
    const dropZone = listDropZone.find(zone => zone.id === dropZoneId);
    if (!dropZone || dropZone.word) return;
    dropZone.word = word;
    const newListDropZone = replaceObjectById(dropZone, listDropZone);
    setListDropZone(newListDropZone);

    const newAnswersState = removeObjectByType(answersState, word, 'id');
    setAnswersState(newAnswersState);
  };

  // Hàm xử lý khi click vào từ trong drop zone
  const handleWordClick = (zoneId: string) => {
    const dropZone = listDropZone.find(zone => zone.id === zoneId);
    if (!dropZone || !dropZone.word) return;

    const cloneAnswersState = [...answersState];
    cloneAnswersState.push(dropZone.word);
    setAnswersState(cloneAnswersState);

    dropZone.word = null;
    const newListDropZone = replaceObjectById(dropZone, listDropZone);
    setListDropZone(newListDropZone);
  };

  // Component các từ để kéo thả
  const DraggableWord = ({word}: {word: Word}) => {
    const translateX = useSharedValue(0);
    const translateY = useSharedValue(0);
    const scale = useSharedValue(1);
    const zIndex = useSharedValue(0);

    const checkAndHandleDrop = (
      eventAbsoluteX: number,
      eventAbsoluteY: number,
    ) => {
      const addSize = 20;
      Object.keys(refDropZone.current).forEach(id => {
        const refCurrent = refDropZone.current[id];
        if (refCurrent) {
          refCurrent.measureInWindow((x, y, width, height) => {
            const wordX = eventAbsoluteX;
            const wordY = eventAbsoluteY;
            const dropZoneX = x;
            const dropZoneY = y + addSize;
            const dropZoneRight = x + width + addSize;
            const dropZoneBottom = y + height + addSize;
            if (
              wordX >= dropZoneX &&
              wordX <= dropZoneRight &&
              wordY >= dropZoneY &&
              wordY <= dropZoneBottom
            ) {
              handleDropToZone(id, word);
            }
          });
        }
      });
    };

    const gestureHandler = useAnimatedGestureHandler({
      onStart: _ => {
        'worklet';
        scale.value = withSpring(1.1);
        zIndex.value = 1000;
      },
      onActive: event => {
        'worklet';
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      },
      onEnd: event => {
        'worklet';
        runOnJS(checkAndHandleDrop)(event.absoluteX, event.absoluteY);

        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        scale.value = withSpring(1);
        zIndex.value = 0;
      },
    });

    const animatedStyle = useAnimatedStyle(() => {
      'worklet';
      return {
        transform: [
          {translateX: translateX.value},
          {translateY: translateY.value},
          {scale: scale.value},
        ],
        zIndex: zIndex.value,
      };
    });

    return (
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={[animatedStyle]}>
          <CardText text={word.text} />
        </Animated.View>
      </PanGestureHandler>
    );
  };

  // Component ô trong drop zone
  const SentenceWord = ({dropZone}: {dropZone: DropZone}) => {
    const isAnswer = dropZone.word;

    return (
      <TouchableOpacity
        ref={ref => {
          refDropZone.current[dropZone.id] = ref;
        }}
        onPress={() => (isAnswer ? handleWordClick(dropZone.id) : null)}
        style={[
          styles.sentenceWord,
          styles.dropZone,
          isAnswer && styles.filledDropZone,
        ]}>
        <Text
          style={[styles.sentenceText, !isAnswer && styles.placeholderText]}>
          {dropZone.word?.text || '...'}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={{flex: 1}}>
      <ImageBackground
        style={{flex: 1}}
        source={require('./assets/background.png')}
        resizeMode="cover">
        <View style={{flex: 1, marginHorizontal: 12}}>
          {/* Header */}
          <View style={{}}>
            <HeadGame
              timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
              isShowSuggest={true}
              onUseHint={() => {}}
              gameId={''}
            />
            <LineProgressBar
              progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
            <View
              style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <CardText text={`Cấp độ ${currentLevel}`} />

              <CountBadge
                current={questionDone}
                total={totalQuestion}></CountBadge>
            </View>
          </View>

          <View style={{marginTop: 16}}>
            <CardTitleGame
              showIcon={true}
              title={currentQuestion?.title || ''}></CardTitleGame>
          </View>

          <View style={{marginTop: 60}}>
            <View>
              <View style={styles.sentenceRow}>
                {React.useMemo(() => {
                  return listDropZone.map(zone => (
                    <SentenceWord key={zone.id} dropZone={zone} />
                  ));
                }, [listDropZone])}
              </View>
              <View style={styles.wordsContainer}>
                {React.useMemo(() => {
                  return answersState.map(word => (
                    <View key={word.id} style={{margin: 4}}>
                      <DraggableWord word={word} />
                    </View>
                  ));
                }, [answersState])}
              </View>
              <TouchableOpacity
                onPress={checkAnswer}
                style={{
                  backgroundColor: '#4CAF50',
                  paddingVertical: 12,
                  paddingHorizontal: 24,
                  borderRadius: 8,
                  marginTop: 20,
                  alignSelf: 'center',
                  elevation: 3,
                }}
                activeOpacity={0.7}>
                <Text
                  style={{
                    color: 'white',
                    fontSize: 16,
                    fontWeight: 'bold',
                    textAlign: 'center',
                  }}>
                  Kiểm tra kết quả
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={{position: 'absolute', bottom: 0}}>
            <BottomGame
              resetGame={startGame}
              backGame={() => {}}
              pauseGame={() => {}}
              volumeGame={() => {}}></BottomGame>
          </View>
        </View>
      </ImageBackground>
      <View style={{zIndex: 1000}}>
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={startGame}
          message={messageGameOver}
          isTimeOut={false}
          isShowHeader={true}
        />
        <GameOverModal
          visible={isQuestionError}
          onClose={() => {}}
          restartGame={restartLevel}
          message={'Tiếc quá sai rồi, làm lại nào'}
          isTimeOut={false}
          isShowCardText={true}
          statusCard={'error'}
          cardText={textAnswer}
        />
        <ModelDescriptionQuestion
          visible={isWinnerQuestion}
          onNext={nextQuestion}
          message={currentQuestion?.description || ''}
        />
        <ModelDoneLevel
          visible={isWinnerLevel}
          message={`Bạn đã vượt qua cấp ${currentLevel}`}
          currentGem={300}
          currentCup={300}
          gemAdd={10}
          cupAdd={10}
          onNextLevel={onNextLevel}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  sentenceContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 15,
    padding: 20,
    marginBottom: 40,
    minHeight: 120,
    justifyContent: 'center',
  },
  sentenceRow: {
    paddingVertical: 20,
    borderRadius: 12,
    backgroundColor: 'white',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sentenceWord: {
    marginHorizontal: 4,
    marginVertical: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  dropZone: {
    backgroundColor: '#F8F8F8',
    borderWidth: 2,
    borderColor: '#DDD',
    borderStyle: 'dashed',
    minWidth: 60,
    minHeight: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filledDropZone: {
    backgroundColor: '#E8F5E8',
    borderColor: '#4CAF50',
    borderStyle: 'solid',
  },
  sentenceText: {
    fontSize: 18,
    color: '#333',
    fontWeight: '600',
    textAlign: 'center',
  },
  placeholderText: {
    color: '#999',
    fontSize: 16,
  },
  wordsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: 20,
  },
});

export default StartMGHH;
